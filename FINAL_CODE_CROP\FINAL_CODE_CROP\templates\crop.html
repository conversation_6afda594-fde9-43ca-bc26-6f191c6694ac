{% extends 'layout.html' %}

{% block content %}
<div class="container-fluid py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); min-height: 100vh;">
  <div class="row align-items-center">
    <div class="col-lg-6 text-center">
      <div class="hero-image fade-in">
        <img
          src="../static/images/bg.svg"
          alt="Crop Recommendation"
          class="img-fluid rounded-4 shadow-lg"
          style="max-height: 500px; width: auto;"
        />
      </div>
    </div>
    <div class="col-lg-6">
      <div class="card card-modern shadow-lg">
        <div class="card-header card-header-modern text-center">
          <h2 class="mb-0">
            <i class="fas fa-seedling me-2"></i>
            AI Crop Recommendation
          </h2>
          <p class="mb-0 mt-2 opacity-75">Find the most suitable crop for your farm</p>
        </div>
        <div class="card-body p-4">

          <form method="POST" action="{{ url_for('crop_predict') }}" class="needs-validation" novalidate>
            <div class="row g-3">
              <div class="col-md-6">
                <label for="Nitrogen" class="form-label fw-semibold">
                  <i class="fas fa-flask text-primary me-1"></i>Nitrogen (N)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Nitrogen"
                  name="nitrogen"
                  value="{{n}}"
                  min="1"
                  max="140"
                  placeholder="Enter nitrogen level"
                  required
                />
                <div class="form-text">Range: 1-140 kg/ha</div>
              </div>

              <div class="col-md-6">
                <label for="Phosphorous" class="form-label fw-semibold">
                  <i class="fas fa-flask text-warning me-1"></i>Phosphorous (P)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Phosphorous"
                  name="phosphorous"
                  value="{{p}}"
                  min="5"
                  max="145"
                  placeholder="Enter phosphorous level"
                  required
                />
                <div class="form-text">Range: 5-145 kg/ha</div>
              </div>

              <div class="col-md-6">
                <label for="Pottasium" class="form-label fw-semibold">
                  <i class="fas fa-flask text-success me-1"></i>Potassium (K)
                </label>
                <input
                  type="number"
                  class="form-control form-control-modern"
                  id="Pottasium"
                  name="pottasium"
                  value="{{k}}"
                  min="5"
                  max="205"
                  placeholder="Enter potassium level"
                  required
                />
                <div class="form-text">Range: 5-205 kg/ha</div>
              </div>
              <div class="col-md-6">
                <label for="ph" class="form-label fw-semibold">
                  <i class="fas fa-vial text-info me-1"></i>pH Level
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="ph"
                  name="ph"
                  min="4"
                  max="10"
                  placeholder="Enter pH level"
                  required
                />
                <div class="form-text">Range: 4.0-10.0</div>
              </div>

              <div class="col-md-6">
                <label for="Rainfall" class="form-label fw-semibold">
                  <i class="fas fa-cloud-rain text-primary me-1"></i>Rainfall (mm)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="Rainfall"
                  name="rainfall"
                  min="20"
                  max="300"
                  placeholder="Enter rainfall amount"
                  required
                />
                <div class="form-text">Range: 20-300 mm</div>
              </div>

              <div class="col-md-6">
                <label for="temp" class="form-label fw-semibold">
                  <i class="fas fa-thermometer-half text-danger me-1"></i>Temperature (°C)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="temp"
                  name="temp"
                  min="8"
                  max="44"
                  value="{{temp}}"
                  placeholder="Enter temperature"
                  required
                />
                <div class="form-text">Range: 8-44°C</div>
              </div>

              <div class="col-md-6">
                <label for="hum" class="form-label fw-semibold">
                  <i class="fas fa-tint text-info me-1"></i>Humidity (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  class="form-control form-control-modern"
                  id="hum"
                  value="{{hum}}"
                  min="14"
                  max="100"
                  name="hum"
                  placeholder="Enter humidity level"
                  required
                />
                <div class="form-text">Range: 14-100%</div>
              </div>
            </div>

            <div class="text-center mt-4">
              <button
                type="submit"
                class="btn btn-primary-modern btn-lg px-5 py-3"
              >
                <i class="fas fa-brain me-2"></i>Get AI Recommendation
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
