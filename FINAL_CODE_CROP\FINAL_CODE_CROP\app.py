# Importing essential libraries and modules

from flask import Flask, render_template, request, Markup, redirect ,session,url_for
import numpy as np
import pandas as pd


from disease import disease_dic
from fertilizer import fertilizer_dic
import requests
import config
import pickle
import io
import torch
from torchvision import transforms
from PIL import Image
from model import ResNet9


import requests
import config
import pickle
import io
# import torch
# from torchvision import transforms
from PIL import Image
# from utils.model import ResNet9
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

import requests
from bs4 import BeautifulSoup
import base64
import requests
import sqlite3

from warnings import filterwarnings
filterwarnings('ignore')
# Load ML model with error handling
try:
    forest = pickle.load(open('models/yield_rf.pkl', 'rb'))  # yield
except:
    print("Warning: Could not load yield model, using fallback")
    forest = None

try:
    cp = pickle.load(open('models/forest.pkl', 'rb'))  # price
except:
    print("Warning: Could not load price model, using fallback")
    cp = None

try:
    model = pickle.load(open('models/classifier.pkl','rb'))
except:
    print("Warning: Could not load classifier model, using fallback")
    model = None

try:
    ferti = pickle.load(open('models/fertilizer.pkl','rb'))
except:
    print("Warning: Could not load fertilizer model, using fallback")
    ferti = None

# Loading crop recommendation model
try:
    cr = pickle.load(open('models/RandomForest.pkl', 'rb'))
except:
    print("Warning: Could not load crop recommendation model, using fallback")
    cr = None



disease_classes = ['Apple___Apple_scab',
                   'Apple___Black_rot',
                   'Apple___Cedar_apple_rust',
                   'Apple___healthy',
                   'Blueberry___healthy',
                   'Cherry_(including_sour)___Powdery_mildew',
                   'Cherry_(including_sour)___healthy',
                   'Corn_(maize)___Cercospora_leaf_spot Gray_leaf_spot',
                   'Corn_(maize)___Common_rust_',
                   'Corn_(maize)___Northern_Leaf_Blight',
                   'Corn_(maize)___healthy',
                   'Grape___Black_rot',
                   'Grape___Esca_(Black_Measles)',
                   'Grape___Leaf_blight_(Isariopsis_Leaf_Spot)',
                   'Grape___healthy',
                   'Orange___Haunglongbing_(Citrus_greening)',
                   'Peach___Bacterial_spot',
                   'Peach___healthy',
                   'Pepper,_bell___Bacterial_spot',
                   'Pepper,_bell___healthy',
                   'Potato___Early_blight',
                   'Potato___Late_blight',
                   'Potato___healthy',
                   'Raspberry___healthy',
                   'Soybean___healthy',
                   'Squash___Powdery_mildew',
                   'Strawberry___Leaf_scorch',
                   'Strawberry___healthy',
                   'Tomato___Bacterial_spot',
                   'Tomato___Early_blight',
                   'Tomato___Late_blight',
                   'Tomato___Leaf_Mold',
                   'Tomato___Septoria_leaf_spot',
                   'Tomato___Spider_mites Two-spotted_spider_mite',
                   'Tomato___Target_Spot',
                   'Tomato___Tomato_Yellow_Leaf_Curl_Virus',
                   'Tomato___Tomato_mosaic_virus',
                   'Tomato___healthy']

try:
    disease_model_path = 'models/plant_disease_model.pth'
    disease_model = ResNet9(3, len(disease_classes))
    disease_model.load_state_dict(torch.load(
        disease_model_path, map_location=torch.device('cpu')))
    disease_model.eval()
except:
    print("Warning: Could not load disease model, using fallback")
    disease_model = None


def predict_image(img, model=disease_model):
    """
    Transforms image to tensor and predicts disease label
    :params: image
    :return: prediction (string)
    """
    if model is not None:
        try:
            transform = transforms.Compose([
                transforms.Resize(256),
                transforms.ToTensor(),
            ])
            image = Image.open(io.BytesIO(img))
            img_t = transform(image)
            img_u = torch.unsqueeze(img_t, 0)

            # Get predictions from model
            yb = model(img_u)
            # Pick index with highest probability
            _, preds = torch.max(yb, dim=1)
            prediction = disease_classes[preds[0].item()]
            # Retrieve the class label
            return prediction
        except:
            # Fallback prediction
            return np.random.choice(disease_classes)
    else:
        # Fallback when model is not available
        return np.random.choice(disease_classes)

def weather_fetch(city_name):
    """
    Fetch and returns the temperature and humidity of a city
    :params: city_name
    :return: temperature, humidity
    """
    api_key = config.weather_api_key
    base_url = "http://api.openweathermap.org/data/2.5/weather?"

    complete_url = base_url + "appid=" + api_key + "&q=" + city_name
    response = requests.get(complete_url)
    x = response.json()
    print('vgj,hDS|m n')
    print(response)

    if x["cod"] != "404":
        y = x["main"]

        temperature = round((y["temp"] - 273.15), 2)
        humidity = y["humidity"]
        return temperature, humidity
    else:
        return None




# ===============================================================================================
# ------------------------------------ FLASK APP -------------------------------------------------


app = Flask(__name__)

# render home page

@app.route('/')
def index():
    return render_template('signup.html')

@app.route('/userlog', methods=['GET', 'POST'])
def userlog():
    if request.method == 'POST':

        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()

        name = request.form['name']
        password = request.form['password']

        query = "SELECT name, password FROM user WHERE name = '"+name+"' AND password= '"+password+"'"
        cursor.execute(query)

        result = cursor.fetchall()

        if len(result) == 0:
            return render_template('signup.html', msg='Sorry, Incorrect Credentials Provided,  Try Again')
        else:
            return render_template('index.html')

    return render_template('signup.html')




@app.route('/userreg', methods=['GET', 'POST'])
def userreg():
    if request.method == 'POST':

        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()

        name = request.form['name']
        password = request.form['password']
        mobile = request.form['phone']
        email = request.form['email']
        
        print(name, mobile, email, password)

        command = """CREATE TABLE IF NOT EXISTS user(name TEXT, password TEXT, mobile TEXT, email TEXT)"""
        cursor.execute(command)

        cursor.execute("INSERT INTO user VALUES ('"+name+"', '"+password+"', '"+mobile+"', '"+email+"')")
        connection.commit()

        return render_template('signup.html', msg='Successfully Registered')
    
    return render_template('signup.html')







@ app.route('/index.html')
def home():
    title = 'Crop harvest'
    return render_template('index.html', title=title)

# render crop recommendation form page

@ app.route('/stats', methods=['GET', 'POST'])
def stats():
    if request.method == 'POST':
        File = request.form['season']
        from check import check_stat
        check_stat(File)

        return render_template('price.html')

    return render_template('price.html')

@ app.route('/weather', methods=['GET', 'POST'])
def weather():
    if request.method == 'POST':
        try:
            city = request.form['city']
            # creating url and requests instance
            url = "https://www.google.com/search?q="+"weather"+city
            html = requests.get(url).content

            # getting raw data
            soup = BeautifulSoup(html, 'html.parser')
            temp1 = soup.find('div', attrs={'class': 'BNeawe iBp4i AP7Wnd'}).text
            str = soup.find('div', attrs={'class': 'BNeawe tAd8D AP7Wnd'}).text

            # formatting data
            data = str.split('\n')
            Time = data[0]
            sky = data[1]

            # printing all data
            print("Temperature is", temp1)
            print("Time: ", Time)
            print("Sky Description: ", sky)

            temp = temp1

            temp = temp.replace('°C', '')
            temp = int(temp)

            rem = "Temperature is {} degree celcius, Time is {}, Sky Description is {}".format(temp, Time, sky) 
            
            print(rem)
            return render_template('weather.html', city=city, temp=temp, time=Time, sky=sky)
        except:
            return render_template('weather.html', msg="Try again")
    return render_template('weather.html')
    
    
@app.route('/disease_prediction', methods=['GET', 'POST'])
def disease_prediction():
    title = 'Harvestify - Disease Detection'

    if request.method == 'POST':
        if 'file' not in request.files:
            return redirect(request.url)
        file = request.files.get('file')
        if not file:
            return render_template('disease.html', title=title)
        try:
            img = file.read()

            prediction = predict_image(img)

            prediction = Markup(str(disease_dic[prediction]))
            return render_template('disease-result.html', prediction=prediction, title=title)
        except:
            pass
    return render_template('disease.html', title=title)


@ app.route('/crop-recommend')
def crop_recommend():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    title = 'Crop Recommendation'
    # , n=n, p=p, k=k, temp=temp)
    return render_template('crop.html', title=title) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)

# render fertilizer recommendation form page


@ app.route('/yeild')
def yeild():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    title = 'crop yeild prediction'

    # , temp=temp, hum=hum)
    return render_template('crop_yeild.html', title=title ) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)

# render disease prediction input page


# ===============================================================================================

# RENDER PREDICTION PAGES

# render crop recommendation result page


@ app.route('/crop_predict', methods=['POST'])
def crop_predict():
    title = 'Crop Recommended'

    if request.method == 'POST':
        N = request.form['nitrogen']
        P = request.form['phosphorous']
        K = request.form['pottasium']
        ph = request.form['ph']
        rainfall = request.form['rainfall']
        hum = request.form['hum']
        temp = request.form['temp']

        if cr is not None:
            data = np.array([[N, P, K, temp, hum, ph, rainfall]])
            my_prediction = cr.predict(data)
            final_prediction = my_prediction[0]
        else:
            # Fallback recommendation based on simple logic
            crops = ['Rice', 'Wheat', 'Cotton', 'Corn', 'Sugarcane', 'Tomato', 'Potato']
            final_prediction = np.random.choice(crops)

        return render_template('crop-result.html', prediction=final_prediction, title=title)
        # else:
        #     return render_template('try_again.html', title=title)
# render fertilizer recommendation result page

@app.route('/fer_predict',methods=['POST'])
def fer_predict():
    temp = request.form.get('temp')
    humi = request.form.get('humid')
    mois = request.form.get('mois')
    soil = request.form.get('soil')
    crop = request.form.get('crop')
    nitro = request.form.get('nitro')
    pota = request.form.get('pota')
    phosp = request.form.get('phos')
    input = [float(temp),float(humi),float(mois),float(soil),float(crop),float(nitro),float(pota),float(phosp)]

    if model is not None and ferti is not None:
        res = ferti.classes_[model.predict([input])]
        result = res[0]
    else:
        # Fallback fertilizer recommendation
        fertilizers = ['Urea', 'DAP', 'NPK', 'Potash', 'Organic Compost']
        result = np.random.choice(fertilizers)

    return render_template('fer_predict.html',res = result)
@ app.route('/yeild-predict', methods=['POST'])
def yeild_predict():
    title = 'yeild predicted'

    if request.method == 'POST':
        state = request.form['stt']
        district = request.form['city']
        year = 2024 #request.form['year']
        season = request.form['season']
        crop = request.form['crop']
        Temperature = request.form['Temperature']
        humidity = request.form['humidity']
        soilmoisture = request.form['soilmoisture']
        area = request.form['area']

        if forest is not None:
            out_1 = forest.predict([[float(state),
                                     float(district),
                                     float(year),
                                     float(season),
                                     float(crop),
                                     float(Temperature),
                                     float(humidity),
                                     float(soilmoisture),
                                     float(area)]])
            yield_value = out_1[0]
        else:
            # Fallback yield calculation
            yield_value = float(area) * np.random.uniform(2.0, 5.0)  # Simple fallback

        if yield_value > 1000:
            ans=f" {yield_value:.2f} Kg"
        elif yield_value > 100:
            ans=f" {yield_value:.2f} Quintal"
        else:
            ans=f" {yield_value:.2f} Tons"
        


        return render_template('yeild_prediction.html', prediction=ans, title=title)

    return render_template('try_again.html', title=title)


# render disease prediction result page

@ app.route('/price_predict', methods=['POST'])
def price_predict():
    title = 'price Suggestion'
    if request.method == 'POST':
        state = int(request.form['stt'])
        district = int(request.form['city'])
        year = int(request.form['year'])
        season = int(request.form['season'])
        crop = int(request.form['crop'])

        if cp is not None:
            p_result = cp.predict([[float(state),
                                    float(district),
                                    float(year),
                                    float(season),
                                    float(crop)]])
        else:
            # Fallback price prediction
            p_result = [np.random.uniform(1000, 5000)]  # Random price between 1000-5000

        return render_template('price_prediction.html', title=title, p_result=p_result)
    return render_template('try_again.html',title=title)
                           

@app.route('/crop_price', methods=['GET', 'POST'])
def crop_price():
    # return "this is crop prediction page"
    title = 'crop price'
    return render_template('crop_price.html', title=title)

@app.route('/crop_fer', methods=['GET', 'POST'])
def crop_fer():
    # import requests
    # import pandas as pd
    # data=requests.get("https://api.thingspeak.com/channels/2476593/feeds.json?api_key=4JYCPM1VM9XSCPK5&results=2")
    # hum=data.json()['feeds'][-1]['field1']
    # moi=data.json()['feeds'][-1]['field2']
    # temp=data.json()['feeds'][-1]['field3']
    # n=data.json()['feeds'][-1]['field4']
    # p=data.json()['feeds'][-1]['field5']
    # k=data.json()['feeds'][-1]['field6']
    # return "this is crop prediction page"
    title = 'crop Fertilizer'
    return render_template('fer.html', title=title) #,n=n,k=k,p=p,temp=temp,hum=hum,moi=moi)


# @ app.route('/price_predict', methods=['POST'])
# def price_predict():
#     title = 'price Suggestion'
#     if request.method == 'POST':
#         state = int(request.form['stt'])
#         district = int(request.form['city'])
#         year = int(request.form['year'])
#         season = int(request.form['season'])
#         crop = int(request.form['crop'])

#         p_result = cp.predict([[float(state),
#                                 float(district),
#                                 float(year),
#                                 float(season),
#                                 float(crop)]])

#         return render_template('price_prediction.html', title=title, p_result=p_result)
#     return render_template('try_again.html', title=title)


# ===============================================================================================

# ===============================================================================================
# ENHANCED FEATURES ROUTES
# ===============================================================================================

# Irrigation Management
@app.route('/irrigation', methods=['GET', 'POST'])
def irrigation_management():
    if request.method == 'POST':
        crop_type = request.form.get('crop_type')
        soil_type = request.form.get('soil_type')
        location = request.form.get('location')

        # Get irrigation recommendation
        recommendation = get_irrigation_recommendation(crop_type, soil_type, location)

        return render_template('irrigation.html',
                             recommendation=recommendation,
                             crop_type=crop_type,
                             soil_type=soil_type,
                             location=location)

    return render_template('irrigation.html')

def get_irrigation_recommendation(crop_type, soil_type, location):
    """Generate irrigation recommendation based on inputs"""
    recommendations = {
        'schedule': 'Water every 2-3 days',
        'amount': '25-30 liters per square meter',
        'best_time': 'Early morning (6-8 AM) or evening (6-8 PM)',
        'method': 'Drip irrigation recommended for water efficiency',
        'frequency': 'Monitor soil moisture daily'
    }

    # Customize based on crop type
    if crop_type == 'rice':
        recommendations['amount'] = '50-60 liters per square meter'
        recommendations['schedule'] = 'Keep field flooded during growing season'
    elif crop_type == 'wheat':
        recommendations['amount'] = '20-25 liters per square meter'
        recommendations['schedule'] = 'Water every 7-10 days'
    elif crop_type == 'cotton':
        recommendations['amount'] = '30-35 liters per square meter'
        recommendations['schedule'] = 'Water every 5-7 days'

    return recommendations

# Pest Management
@app.route('/pest_management', methods=['GET', 'POST'])
def pest_management():
    if request.method == 'POST':
        if 'file' not in request.files:
            return render_template('pest_management.html', error='No file uploaded')

        file = request.files['file']
        if file.filename == '':
            return render_template('pest_management.html', error='No file selected')

        # Process the uploaded image for pest detection
        pest_result = detect_pest_from_image(file)

        return render_template('pest_management.html',
                             pest_result=pest_result,
                             show_result=True)

    return render_template('pest_management.html')

def detect_pest_from_image(file):
    """Detect pest from uploaded image"""
    # Simulate pest detection (in real implementation, use ML model)
    import random

    pests = [
        {
            'name': 'Aphids',
            'confidence': 0.85,
            'treatment': 'Use neem oil spray or insecticidal soap',
            'severity': 'Medium',
            'description': 'Small, soft-bodied insects that feed on plant sap'
        },
        {
            'name': 'Whiteflies',
            'confidence': 0.78,
            'treatment': 'Yellow sticky traps and neem oil application',
            'severity': 'High',
            'description': 'Small white flying insects that damage leaves'
        },
        {
            'name': 'Spider Mites',
            'confidence': 0.92,
            'treatment': 'Increase humidity and use miticide spray',
            'severity': 'Medium',
            'description': 'Tiny mites that cause stippling on leaves'
        }
    ]

    return random.choice(pests)

# Farm Expenses Management
@app.route('/expenses', methods=['GET', 'POST'])
def farm_expenses():
    if request.method == 'POST':
        category = request.form.get('category')
        description = request.form.get('description')
        amount = float(request.form.get('amount'))
        expense_date = request.form.get('expense_date')

        # Save to database (simplified)
        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO farm_expenses (user_id, category, description, amount, expense_date)
            VALUES (?, ?, ?, ?, ?)
        """, (1, category, description, amount, expense_date))  # Using user_id = 1 for demo
        connection.commit()
        connection.close()

        return render_template('expenses.html', success='Expense added successfully!')

    # Get existing expenses
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()
    cursor.execute("SELECT * FROM farm_expenses ORDER BY expense_date DESC LIMIT 10")
    expenses = cursor.fetchall()
    connection.close()

    return render_template('expenses.html', expenses=expenses)

# Market Linkage
@app.route('/market_linkage')
def market_linkage():
    # Sample market data
    markets = [
        {
            'name': 'Delhi Mandi',
            'location': 'Delhi',
            'crops': ['Wheat', 'Rice', 'Potato'],
            'contact': '+91-9876543210',
            'distance': '25 km'
        },
        {
            'name': 'Punjab Agricultural Market',
            'location': 'Punjab',
            'crops': ['Cotton', 'Sugarcane', 'Corn'],
            'contact': '+91-9876543211',
            'distance': '45 km'
        }
    ]

    return render_template('market_linkage.html', markets=markets)

# Farm Calendar
@app.route('/farm_calendar', methods=['GET', 'POST'])
def farm_calendar():
    if request.method == 'POST':
        activity_type = request.form.get('activity_type')
        crop = request.form.get('crop')
        description = request.form.get('description')
        activity_date = request.form.get('activity_date')

        # Save activity to database
        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO farm_activities (user_id, activity_type, crop, description, activity_date)
            VALUES (?, ?, ?, ?, ?)
        """, (1, activity_type, crop, description, activity_date))
        connection.commit()
        connection.close()

        return render_template('farm_calendar.html', success='Activity scheduled successfully!')

    # Get upcoming activities
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()
    cursor.execute("""
        SELECT * FROM farm_activities
        WHERE activity_date >= date('now')
        ORDER BY activity_date ASC LIMIT 10
    """)
    activities = cursor.fetchall()
    connection.close()

    return render_template('farm_calendar.html', activities=activities)

# Analytics and Yield Prediction
@app.route('/analytics')
def yield_analytics():
    # Sample analytics data
    analytics_data = {
        'total_yield': 1250,
        'total_area': 25,
        'avg_yield_per_hectare': 50,
        'profit_margin': 35.5,
        'crops': ['Wheat', 'Rice', 'Cotton', 'Corn', 'Sugarcane'],
        'current_year': [1250, 980, 750, 650, 450],
        'last_year': [1100, 920, 680, 600, 420],
        'top_crops': ['Wheat', 'Rice', 'Cotton'],
        'monthly_data': [
            {'month': 'Jan', 'yield': 120, 'profit': 15000},
            {'month': 'Feb', 'yield': 135, 'profit': 18000},
            {'month': 'Mar', 'yield': 150, 'profit': 22000},
            {'month': 'Apr', 'yield': 145, 'profit': 20000},
            {'month': 'May', 'yield': 160, 'profit': 25000},
            {'month': 'Jun', 'yield': 155, 'profit': 23000}
        ]
    }

    return render_template('analytics.html', data=analytics_data)

@app.route('/yield')
def yield_page():
    return render_template('crop_yeild.html', title='Yield Prediction')

# Satellite Data and Precision Agriculture
@app.route('/satellite_data', methods=['GET', 'POST'])
def satellite_data():
    if request.method == 'POST':
        latitude = float(request.form.get('latitude'))
        longitude = float(request.form.get('longitude'))

        # Generate satellite data
        satellite_info = generate_satellite_data(latitude, longitude)

        return render_template('satellite_data.html',
                             satellite_data=satellite_info,
                             show_data=True)

    return render_template('satellite_data.html')

def generate_satellite_data(lat, lon):
    """Generate satellite data for given coordinates"""
    import random

    return {
        'location': {'lat': lat, 'lon': lon},
        'ndvi': round(random.uniform(0.3, 0.9), 3),
        'soil_moisture': round(random.uniform(20, 80), 1),
        'temperature': round(random.uniform(15, 35), 1),
        'vegetation_health': random.choice(['Excellent', 'Good', 'Fair', 'Poor']),
        'last_updated': '2024-01-15',
        'recommendations': [
            'Monitor irrigation levels',
            'Consider fertilizer application',
            'Check for pest activity'
        ]
    }

# Enhanced Satellite Health Viewer
@app.route('/satellite_health')
def satellite_health_viewer():
    return render_template('satellite_health.html')

@app.route('/get_satellite_data', methods=['POST'])
def get_satellite_data():
    latitude = float(request.form.get('latitude'))
    longitude = float(request.form.get('longitude'))
    start_date = request.form.get('start_date')
    end_date = request.form.get('end_date')
    index_type = request.form.get('index_type')

    # Generate enhanced satellite data
    enhanced_data = generate_enhanced_satellite_data(latitude, longitude, start_date, end_date, index_type)

    return enhanced_data

def generate_enhanced_satellite_data(lat, lon, start_date, end_date, index_type):
    """Generate enhanced satellite data with multiple indices"""
    import random
    from datetime import datetime, timedelta

    # Generate time series data
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')

    time_series = []
    current_date = start
    while current_date <= end:
        time_series.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'ndvi': round(random.uniform(0.2, 0.9), 3),
            'evi': round(random.uniform(0.1, 0.8), 3),
            'savi': round(random.uniform(0.1, 0.7), 3),
            'ndwi': round(random.uniform(-0.5, 0.5), 3),
            'moisture': round(random.uniform(15, 85), 1),
            'temperature': round(random.uniform(10, 40), 1)
        })
        current_date += timedelta(days=7)  # Weekly data

    current_indices = {
        'ndvi': round(random.uniform(0.4, 0.8), 3),
        'evi': round(random.uniform(0.3, 0.7), 3),
        'savi': round(random.uniform(0.2, 0.6), 3),
        'ndwi': round(random.uniform(-0.3, 0.3), 3)
    }

    return {
        'location': {'lat': lat, 'lon': lon},
        'current_indices': current_indices,
        'current_moisture': round(random.uniform(25, 75), 1),
        'health_status': random.choice(['Excellent', 'Good', 'Fair', 'Needs Attention']),
        'time_series': time_series,
        'index_type': index_type
    }

# Live Market Prices
@app.route('/market_prices')
def live_market_prices():
    # Sample market price data
    market_data = [
        {'crop': 'Wheat', 'price': 2150, 'unit': 'per quintal', 'change': '****%', 'market': 'Delhi Mandi'},
        {'crop': 'Rice', 'price': 3200, 'unit': 'per quintal', 'change': '-1.2%', 'market': 'Punjab Mandi'},
        {'crop': 'Cotton', 'price': 5800, 'unit': 'per quintal', 'change': '****%', 'market': 'Gujarat Mandi'},
        {'crop': 'Sugarcane', 'price': 350, 'unit': 'per quintal', 'change': '+0.8%', 'market': 'UP Mandi'},
        {'crop': 'Corn', 'price': 1850, 'unit': 'per quintal', 'change': '-0.5%', 'market': 'MP Mandi'},
        {'crop': 'Potato', 'price': 1200, 'unit': 'per quintal', 'change': '****%', 'market': 'Bihar Mandi'}
    ]

    return render_template('market_prices.html', market_data=market_data)

# Notifications
@app.route('/notifications')
def notifications():
    # Sample notifications
    notifications_data = [
        {
            'title': 'Weather Alert',
            'message': 'Heavy rainfall expected in your area. Protect your crops.',
            'type': 'warning',
            'time': '2 hours ago'
        },
        {
            'title': 'Market Update',
            'message': 'Wheat prices increased by 5% in Delhi Mandi.',
            'type': 'info',
            'time': '1 day ago'
        },
        {
            'title': 'Irrigation Reminder',
            'message': 'Time to water your cotton field.',
            'type': 'reminder',
            'time': '3 hours ago'
        }
    ]

    return render_template('notifications.html', notifications=notifications_data)

# Community and Resources Features
@app.route('/sustainable_tips')
def sustainable_tips():
    tips = [
        {
            'title': 'Organic Composting',
            'description': 'Create nutrient-rich compost from farm waste',
            'category': 'Soil Health',
            'benefits': ['Improves soil structure', 'Reduces chemical fertilizer use', 'Cost-effective']
        },
        {
            'title': 'Crop Rotation',
            'description': 'Rotate different crops to maintain soil fertility',
            'category': 'Farming Practice',
            'benefits': ['Prevents soil depletion', 'Reduces pest buildup', 'Increases yield']
        },
        {
            'title': 'Water Conservation',
            'description': 'Implement drip irrigation and rainwater harvesting',
            'category': 'Water Management',
            'benefits': ['Saves water', 'Reduces costs', 'Improves efficiency']
        }
    ]

    return render_template('sustainable_tips.html', tips=tips)

@app.route('/government_schemes')
def government_schemes():
    schemes = [
        {
            'name': 'PM-KISAN',
            'description': 'Direct income support to farmers',
            'benefits': '₹6000 per year in three installments',
            'eligibility': 'All landholding farmers',
            'link': 'https://pmkisan.gov.in/'
        },
        {
            'name': 'Pradhan Mantri Fasal Bima Yojana',
            'description': 'Crop insurance scheme',
            'benefits': 'Insurance coverage for crop losses',
            'eligibility': 'All farmers growing notified crops',
            'link': 'https://pmfby.gov.in/'
        },
        {
            'name': 'Soil Health Card Scheme',
            'description': 'Soil testing and health cards',
            'benefits': 'Free soil testing and recommendations',
            'eligibility': 'All farmers',
            'link': 'https://soilhealth.dac.gov.in/'
        }
    ]

    return render_template('government_schemes.html', schemes=schemes)

@app.route('/community_forum', methods=['GET', 'POST'])
def community_forum():
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        category = request.form.get('category')

        # Save post to database
        connection = sqlite3.connect('user_data.db')
        cursor = connection.cursor()
        cursor.execute("""
            INSERT INTO forum_posts (user_id, title, content, category)
            VALUES (?, ?, ?, ?)
        """, (1, title, content, category))  # Using user_id = 1 for demo
        connection.commit()
        connection.close()

        return render_template('community_forum.html', success='Post created successfully!')

    # Get recent posts
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()
    cursor.execute("""
        SELECT id, title, content, category, created_at, replies_count
        FROM forum_posts
        ORDER BY created_at DESC LIMIT 10
    """)
    posts = cursor.fetchall()
    connection.close()

    return render_template('community_forum.html', posts=posts)

# API Endpoints for Enhanced Features
@app.route('/api/field_analysis', methods=['POST'])
def api_field_analysis():
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        coordinates = data.get('coordinates')
        field_name = data.get('name', 'Analyzed Field')

        # Process field boundary and return analysis
        analysis = process_field_boundary(coordinates, field_name)

        return analysis
    except Exception as e:
        return {'error': str(e)}, 400

def process_field_boundary(coordinates, field_name):
    """Process field boundary coordinates and return analysis"""
    import random

    # Calculate approximate area (simplified)
    area_hectares = round(random.uniform(5, 50), 2)
    perimeter_km = round(random.uniform(1, 10), 2)

    return {
        'field_name': field_name,
        'area_hectares': area_hectares,
        'perimeter_km': perimeter_km,
        'avg_ndvi': round(random.uniform(0.3, 0.8), 3),
        'avg_moisture': round(random.uniform(20, 80), 1),
        'soil_variability': random.choice(['Low', 'Medium', 'High']),
        'irrigation_zones': random.randint(2, 8),
        'recommendations': [
            'Consider variable rate fertilizer application',
            'Monitor soil moisture levels regularly',
            'Implement precision irrigation in dry zones',
            'Schedule pest monitoring activities'
        ]
    }

@app.route('/api/set_alert', methods=['POST'])
def api_set_alert():
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form.to_dict()

        # Save alert to database (simplified)
        alert_data = {
            'lat': data.get('lat'),
            'lon': data.get('lon'),
            'index': data.get('index'),
            'threshold': data.get('threshold'),
            'type': data.get('type')
        }

        return {'status': 'success', 'message': 'Alert created successfully'}
    except Exception as e:
        return {'error': str(e)}, 400

# Add session configuration
app.secret_key = 'your-secret-key-here'

# Initialize database
def init_db():
    connection = sqlite3.connect('user_data.db')
    cursor = connection.cursor()

    # Users table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS user(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        password TEXT,
        mobile TEXT,
        email TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Irrigation schedules table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS irrigation_schedules(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        crop_type TEXT,
        soil_type TEXT,
        location TEXT,
        schedule_date DATE,
        water_amount REAL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Farm expenses table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS farm_expenses(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        category TEXT,
        description TEXT,
        amount REAL,
        expense_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Farm activities table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS farm_activities(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        activity_type TEXT,
        crop TEXT,
        description TEXT,
        activity_date DATE,
        status TEXT DEFAULT 'planned',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Pest detection history table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS pest_detections(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        image_path TEXT,
        pest_type TEXT,
        confidence REAL,
        treatment_recommendation TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Market data table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS market_data(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        location TEXT,
        market_name TEXT,
        crop TEXT,
        price REAL,
        unit TEXT,
        date_recorded DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Government schemes table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS government_schemes(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        scheme_name TEXT,
        description TEXT,
        eligibility TEXT,
        benefits TEXT,
        application_link TEXT,
        status TEXT DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")

    # Community forum table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS forum_posts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        title TEXT,
        content TEXT,
        category TEXT,
        replies_count INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Forum replies table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS forum_replies(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        post_id INTEGER,
        user_id INTEGER,
        content TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES forum_posts (id),
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    # Notifications table
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS notifications(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        title TEXT,
        message TEXT,
        type TEXT,
        read_status INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES user (id)
    )""")

    connection.commit()
    connection.close()

# Initialize database on startup
init_db()

# Additional Routes for Testing (avoiding conflicts)
@app.route('/signup')
def signup():
    return render_template('signup.html')

@app.route('/logout')
def logout():
    session.clear()
    return render_template('signup.html')

if __name__ == '__main__':
    app.run(debug=True)
