<!DOCTYPE html>
<html lang="en">

<head>
	<title>AgroPro - Smart Agricultural Management Platform</title>
	<link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}"/>

	<!-- Meta Tags -->
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta charset="utf-8">
	<meta name="description" content="AgroPro - Advanced Agricultural Management Platform with AI-powered crop recommendations, satellite monitoring, and precision farming tools">
	<meta name="keywords" content="Agriculture, Smart Farming, Crop Management, AI Agriculture, Precision Farming, Satellite Monitoring, Farm Management">
	<meta name="author" content="AgroPro Team">

	<!-- Bootstrap 5 CSS -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<!-- Font Awesome -->
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
	<!-- Google Fonts -->
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

	<!-- Custom Styles -->
	<style>
		:root {
			--primary-color: #2d5a27;
			--secondary-color: #4a7c59;
			--accent-color: #7fb069;
			--success-color: #28a745;
			--warning-color: #ffc107;
			--danger-color: #dc3545;
			--info-color: #17a2b8;
			--light-color: #f8f9fa;
			--dark-color: #343a40;
			--gradient-primary: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
			--gradient-secondary: linear-gradient(135deg, #7fb069 0%, #a8d5ba 100%);
			--shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
			--shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
			--shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
		}

		* {
			margin: 0;
			padding: 0;
			box-sizing: border-box;
		}

		body {
			font-family: 'Inter', sans-serif;
			line-height: 1.6;
			color: var(--dark-color);
			background-color: #ffffff;
		}

		h1, h2, h3, h4, h5, h6 {
			font-family: 'Poppins', sans-serif;
			font-weight: 600;
		}

		/* Modern Navigation Styles */
		.navbar-modern {
			background: var(--gradient-primary);
			backdrop-filter: blur(10px);
			box-shadow: var(--shadow-md);
			padding: 1rem 0;
			transition: all 0.3s ease;
		}

		.navbar-modern.scrolled {
			padding: 0.5rem 0;
			background: rgba(45, 90, 39, 0.95);
		}

		.navbar-brand-modern {
			font-family: 'Poppins', sans-serif;
			font-weight: 700;
			font-size: 1.8rem;
			color: #ffffff !important;
			text-decoration: none;
			display: flex;
			align-items: center;
			gap: 0.5rem;
		}

		.navbar-brand-modern:hover {
			color: var(--accent-color) !important;
			transform: scale(1.05);
			transition: all 0.3s ease;
		}

		.navbar-nav-modern .nav-link {
			color: #ffffff !important;
			font-weight: 500;
			padding: 0.5rem 1rem !important;
			margin: 0 0.25rem;
			border-radius: 8px;
			transition: all 0.3s ease;
			position: relative;
		}

		.navbar-nav-modern .nav-link:hover {
			background-color: rgba(255, 255, 255, 0.1);
			color: var(--accent-color) !important;
			transform: translateY(-2px);
		}

		.navbar-nav-modern .nav-link.active {
			background-color: var(--accent-color);
			color: var(--dark-color) !important;
		}

		/* Dropdown Styles */
		.dropdown-menu-modern {
			background: #ffffff;
			border: none;
			border-radius: 12px;
			box-shadow: var(--shadow-lg);
			padding: 0.5rem;
			margin-top: 0.5rem;
		}

		.dropdown-item-modern {
			padding: 0.75rem 1rem;
			border-radius: 8px;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 0.75rem;
		}

		.dropdown-item-modern:hover {
			background-color: var(--light-color);
			color: var(--primary-color);
			transform: translateX(5px);
		}

		/* Mobile Navigation */
		.navbar-toggler-modern {
			border: none;
			padding: 0.25rem 0.5rem;
		}

		.navbar-toggler-modern:focus {
			box-shadow: none;
		}

		.navbar-toggler-icon-modern {
			background-image: none;
			width: 24px;
			height: 2px;
			background-color: #ffffff;
			border-radius: 2px;
			transition: all 0.3s ease;
			position: relative;
		}

		.navbar-toggler-icon-modern::before,
		.navbar-toggler-icon-modern::after {
			content: '';
			position: absolute;
			width: 24px;
			height: 2px;
			background-color: #ffffff;
			border-radius: 2px;
			transition: all 0.3s ease;
		}

		.navbar-toggler-icon-modern::before {
			top: -8px;
		}

		.navbar-toggler-icon-modern::after {
			bottom: -8px;
		}

		/* Responsive Design */
		@media (max-width: 991.98px) {
			.navbar-nav-modern {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 12px;
				padding: 1rem;
				margin-top: 1rem;
			}

			.navbar-nav-modern .nav-link {
				color: var(--dark-color) !important;
				margin: 0.25rem 0;
			}

			.navbar-nav-modern .nav-link:hover {
				background-color: var(--light-color);
				color: var(--primary-color) !important;
			}
		}
	</style>
	<!-- Bootstrap 5 JS -->
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
	<!-- jQuery -->
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<!-- Chart.js for analytics -->
	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<!-- Custom Scripts -->
	<script type="text/JavaScript" src="{{ url_for('static', filename='scripts/cities.js') }}"></script>

	<!-- Additional Styles -->
	<style>
		/* Content Area Styles */
		.content-wrapper {
			min-height: calc(100vh - 80px);
			padding-top: 2rem;
		}

		/* Card Styles */
		.card-modern {
			border: none;
			border-radius: 16px;
			box-shadow: var(--shadow-sm);
			transition: all 0.3s ease;
			overflow: hidden;
		}

		.card-modern:hover {
			box-shadow: var(--shadow-md);
			transform: translateY(-4px);
		}

		.card-header-modern {
			background: var(--gradient-primary);
			color: #ffffff;
			border: none;
			padding: 1.5rem;
			font-weight: 600;
		}

		/* Button Styles */
		.btn-modern {
			border-radius: 12px;
			padding: 0.75rem 1.5rem;
			font-weight: 500;
			transition: all 0.3s ease;
			border: none;
		}

		.btn-primary-modern {
			background: var(--gradient-primary);
			color: #ffffff;
		}

		.btn-primary-modern:hover {
			background: var(--gradient-secondary);
			transform: translateY(-2px);
			box-shadow: var(--shadow-md);
		}

		/* Form Styles */
		.form-control-modern {
			border-radius: 12px;
			border: 2px solid #e9ecef;
			padding: 0.75rem 1rem;
			transition: all 0.3s ease;
		}

		.form-control-modern:focus {
			border-color: var(--primary-color);
			box-shadow: 0 0 0 0.2rem rgba(45, 90, 39, 0.25);
		}

		/* Animation Classes */
		.fade-in {
			animation: fadeIn 0.6s ease-in;
		}

		@keyframes fadeIn {
			from { opacity: 0; transform: translateY(20px); }
			to { opacity: 1; transform: translateY(0); }
		}

		.slide-in {
			animation: slideIn 0.6s ease-out;
		}

		@keyframes slideIn {
			from { opacity: 0; transform: translateX(-30px); }
			to { opacity: 1; transform: translateX(0); }
		}
	</style>
</head>

<body>
	<!-- Modern Navigation -->
	<nav class="navbar navbar-expand-lg navbar-modern fixed-top" id="mainNavbar">
		<div class="container">
			<a class="navbar-brand-modern" href="{{ url_for('home') }}">
				<i class="fas fa-seedling"></i>
				AgroPro Platform
			</a>

			<button class="navbar-toggler navbar-toggler-modern" type="button" data-bs-toggle="collapse"
					data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false"
					aria-label="Toggle navigation">
				<span class="navbar-toggler-icon-modern"></span>
			</button>

			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav navbar-nav-modern ms-auto">
					<li class="nav-item">
						<a class="nav-link active" href="{{ url_for('home') }}">
							<i class="fas fa-home me-1"></i> Home
						</a>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="cropDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-seedling me-1"></i> Crop Management
						</a>
						<ul class="dropdown-menu dropdown-menu-modern">
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_recommend') }}">
								<i class="fas fa-brain"></i> AI Crop Recommendation</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('disease_prediction') }}">
								<i class="fas fa-bug"></i> Disease Detection</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_fer') }}">
								<i class="fas fa-flask"></i> Fertilizer Advisor</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('pest_management') }}">
								<i class="fas fa-shield-alt"></i> Pest Management</a></li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="farmDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-tractor me-1"></i> Farm Tools
						</a>
						<ul class="dropdown-menu dropdown-menu-modern">
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('irrigation_management') }}">
								<i class="fas fa-tint"></i> Irrigation Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('farm_expenses') }}">
								<i class="fas fa-calculator"></i> Expense Management</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('farm_calendar') }}">
								<i class="fas fa-calendar-alt"></i> Farm Calendar</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('market_linkage') }}">
								<i class="fas fa-handshake"></i> Market Linkage</a></li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="analyticsDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-chart-line me-1"></i> Analytics
						</a>
						<ul class="dropdown-menu dropdown-menu-modern">
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('yield_analytics') }}">
								<i class="fas fa-chart-bar"></i> Yield Analytics</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('yield_page') }}">
								<i class="fas fa-chart-line"></i> Yield Prediction</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('crop_price') }}">
								<i class="fas fa-rupee-sign"></i> Price Prediction</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('satellite_health_viewer') }}">
								<i class="fas fa-satellite"></i> Satellite Health</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('satellite_data') }}">
								<i class="fas fa-globe"></i> Precision Agriculture</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('live_market_prices') }}">
								<i class="fas fa-coins"></i> Market Prices</a></li>
						</ul>
					</li>

					<li class="nav-item dropdown">
						<a class="nav-link dropdown-toggle" href="#" id="resourcesDropdown" role="button"
						   data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fas fa-book me-1"></i> Resources
						</a>
						<ul class="dropdown-menu dropdown-menu-modern">
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('sustainable_tips') }}">
								<i class="fas fa-leaf"></i> Sustainable Tips</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('government_schemes') }}">
								<i class="fas fa-university"></i> Government Schemes</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('community_forum') }}">
								<i class="fas fa-users"></i> Community Forum</a></li>
							<li><a class="dropdown-item dropdown-item-modern" href="{{ url_for('notifications') }}">
								<i class="fas fa-bell"></i> Notifications</a></li>
						</ul>
					</li>

					<li class="nav-item">
						<a class="nav-link" href="{{ url_for('weather') }}">
							<i class="fas fa-cloud-sun me-1"></i> Weather
						</a>
					</li>

					<li class="nav-item">
						<a class="nav-link" href="{{ url_for('logout') }}">
							<i class="fas fa-sign-out-alt me-1"></i> Logout
						</a>
					</li>
				</ul>
			</div>
		</div>
	</nav>

	<!-- Content Wrapper -->
	<div class="content-wrapper">
		{% block content %}{% endblock %}
		{% block body %}{% endblock %}
	</div>

	<!-- Modern Footer -->
	<footer class="bg-dark text-light py-4 mt-5">
		<div class="container">
			<div class="row">
				<div class="col-md-6">
					<h5 class="text-success">
						<i class="fas fa-seedling me-2"></i>AgroPro Platform
					</h5>
					<p class="mb-0">Empowering farmers with AI-driven agricultural solutions</p>
				</div>
				<div class="col-md-6 text-md-end">
					<p class="mb-0">&copy; 2024 AgroPro. All rights reserved.</p>
					<small class="text-muted">Smart Agriculture • Precision Farming • Sustainable Growth</small>
				</div>
			</div>
		</div>
	</footer>

	<!-- Custom JavaScript -->
	<script>
		// Navbar scroll effect
		window.addEventListener('scroll', function() {
			const navbar = document.getElementById('mainNavbar');
			if (window.scrollY > 50) {
				navbar.classList.add('scrolled');
			} else {
				navbar.classList.remove('scrolled');
			}
		});

		// Add fade-in animation to content
		document.addEventListener('DOMContentLoaded', function() {
			const contentWrapper = document.querySelector('.content-wrapper');
			if (contentWrapper) {
				contentWrapper.classList.add('fade-in');
			}
		});

		// Smooth scrolling for anchor links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				e.preventDefault();
				const target = document.querySelector(this.getAttribute('href'));
				if (target) {
					target.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}
			});
		});
	</script>
</body>

</html>