<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - AgroPro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('home') }}">
                <i class="fas fa-seedling text-success me-2" style="font-size: 24px;"></i>
                <span class="fw-bold" style="font-size: 22px;">AgroPro</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-5 fw-bold text-primary">
                        <i class="fas fa-chart-line me-3"></i>Yield & Price Analytics
                    </h1>
                    <p class="lead text-muted">Data-driven insights for better farming decisions</p>
                </div>

                <!-- Key Metrics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <h5>Average Yield</h5>
                                <h3>3.2 T/Ha</h3>
                                <small>+8% from last year</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-rupee-sign fa-2x mb-2"></i>
                                <h5>Avg Price</h5>
                                <h3>₹2,180</h3>
                                <small>+5% from last month</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-trending-up fa-2x mb-2"></i>
                                <h5>Best Performer</h5>
                                <h3>Corn</h3>
                                <small>4.1 T/Ha yield</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card border-0 shadow-sm bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <h5>Season</h5>
                                <h3>Kharif</h3>
                                <small>Current season</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row">
                    <!-- Yield Comparison Chart -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Yield Comparison (T/Ha)</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="yieldChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Price Trends Chart -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-lg">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Price Trends (₹/Quintal)</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="priceChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Analytics Table -->
                <div class="card mt-4 border-0 shadow-lg">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Detailed Crop Analytics</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Crop</th>
                                        <th>Current Year Yield (T/Ha)</th>
                                        <th>Last Year Yield (T/Ha)</th>
                                        <th>Yield Change</th>
                                        <th>Current Price (₹/Q)</th>
                                        <th>Price Trend</th>
                                        <th>Profitability</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for i in range(data.crops|length) %}
                                    <tr>
                                        <td>
                                            <strong>{{ data.crops[i] }}</strong>
                                        </td>
                                        <td>{{ data.current_year[i] }}</td>
                                        <td>{{ data.last_year[i] }}</td>
                                        <td>
                                            {% set change = ((data.current_year[i] - data.last_year[i]) / data.last_year[i] * 100) %}
                                            {% if change > 0 %}
                                                <span class="badge bg-success">+{{ "%.1f"|format(change) }}%</span>
                                            {% else %}
                                                <span class="badge bg-danger">{{ "%.1f"|format(change) }}%</span>
                                            {% endif %}
                                        </td>
                                        <td>₹{{ data.price_trends[i] }}</td>
                                        <td>
                                            <i class="fas fa-arrow-up text-success"></i>
                                            <small class="text-success">+3%</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">High</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Regional Analysis -->
                <div class="card mt-4 border-0 shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-map me-2"></i>Regional Performance Analysis</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="text-primary">North India</h6>
                                        <div class="mb-2">
                                            <i class="fas fa-seedling fa-2x text-success"></i>
                                        </div>
                                        <p class="mb-1"><strong>Best Crop:</strong> Wheat</p>
                                        <p class="mb-1"><strong>Avg Yield:</strong> 3.5 T/Ha</p>
                                        <p class="mb-0"><strong>Price:</strong> ₹2,100/Q</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="text-success">South India</h6>
                                        <div class="mb-2">
                                            <i class="fas fa-seedling fa-2x text-primary"></i>
                                        </div>
                                        <p class="mb-1"><strong>Best Crop:</strong> Rice</p>
                                        <p class="mb-1"><strong>Avg Yield:</strong> 2.8 T/Ha</p>
                                        <p class="mb-0"><strong>Price:</strong> ₹1,850/Q</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <h6 class="text-warning">West India</h6>
                                        <div class="mb-2">
                                            <i class="fas fa-seedling fa-2x text-warning"></i>
                                        </div>
                                        <p class="mb-1"><strong>Best Crop:</strong> Cotton</p>
                                        <p class="mb-1"><strong>Avg Yield:</strong> 1.9 T/Ha</p>
                                        <p class="mb-0"><strong>Price:</strong> ₹5,200/Q</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Market Predictions -->
                <div class="card mt-4 border-0 shadow-lg">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-crystal-ball me-2"></i>Market Predictions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Next Month Forecast</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-arrow-up text-success me-2"></i>
                                        <strong>Rice:</strong> Expected price increase of 5-8%
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-arrow-down text-danger me-2"></i>
                                        <strong>Wheat:</strong> Slight price decrease of 2-3%
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-arrow-up text-success me-2"></i>
                                        <strong>Corn:</strong> Stable with 1-2% increase
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-success">Seasonal Recommendations</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Focus on high-yield varieties
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Consider crop diversification
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Monitor weather patterns closely
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="text-center mt-4 mb-4">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary">
                            <i class="fas fa-download me-1"></i>Export PDF
                        </button>
                        <button type="button" class="btn btn-outline-success">
                            <i class="fas fa-file-excel me-1"></i>Export Excel
                        </button>
                        <button type="button" class="btn btn-outline-info">
                            <i class="fas fa-share me-1"></i>Share Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js Scripts -->
    <script>
        // Yield Comparison Chart
        const yieldCtx = document.getElementById('yieldChart').getContext('2d');
        const yieldChart = new Chart(yieldCtx, {
            type: 'bar',
            data: {
                labels: {{ data.crops | tojson }},
                datasets: [{
                    label: 'Current Year',
                    data: {{ data.current_year | tojson }},
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }, {
                    label: 'Last Year',
                    data: {{ data.last_year | tojson }},
                    backgroundColor: 'rgba(255, 99, 132, 0.8)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Yield (Tonnes/Hectare)'
                        }
                    }
                }
            }
        });

        // Price Trends Chart
        const priceCtx = document.getElementById('priceChart').getContext('2d');
        const priceChart = new Chart(priceCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Rice',
                    data: [1800, 1820, 1850, 1830, 1860, 1850],
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.4
                }, {
                    label: 'Wheat',
                    data: [2050, 2080, 2100, 2120, 2110, 2100],
                    borderColor: 'rgba(255, 206, 86, 1)',
                    backgroundColor: 'rgba(255, 206, 86, 0.2)',
                    tension: 0.4
                }, {
                    label: 'Corn',
                    data: [1900, 1920, 1950, 1940, 1960, 1950],
                    borderColor: 'rgba(153, 102, 255, 1)',
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Price (₹/Quintal)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
