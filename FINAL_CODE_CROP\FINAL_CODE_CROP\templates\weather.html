{% extends 'layout.html' %}

{% block content %}
<div class="container-fluid py-4" style="background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%); min-height: 100vh;">
    <!-- Enhanced Weather Header -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-white mb-3">
                <i class="fas fa-cloud-sun me-3"></i>Agricultural Weather Center
            </h1>
            <p class="lead text-white">Real-time weather data and agricultural forecasts for smart farming decisions</p>
        </div>
    </div>
    <!-- Weather Search Section -->
    <div class="row mb-5">
        <div class="col-lg-4 mx-auto">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-search me-2"></i>Get Weather Data
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/weather" class="needs-validation" novalidate>
                        <!-- City Selection Method -->
                        <div class="mb-3">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>Select Location Method
                            </label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="location_method" id="dropdown_method" value="dropdown" checked>
                                <label class="btn btn-outline-primary" for="dropdown_method">
                                    <i class="fas fa-list me-1"></i>Select City
                                </label>

                                <input type="radio" class="btn-check" name="location_method" id="manual_method" value="manual">
                                <label class="btn btn-outline-primary" for="manual_method">
                                    <i class="fas fa-keyboard me-1"></i>Type City
                                </label>
                            </div>
                        </div>

                        <!-- Dropdown City Selection -->
                        <div class="mb-3" id="city_dropdown_section">
                            <label for="city_select" class="form-label fw-semibold">
                                <i class="fas fa-city text-success me-2"></i>Choose City
                            </label>
                            <select class="form-select form-select-lg" id="city_select" name="city_select">
                                <option value="">-- Select a City --</option>

                                <!-- Indian Agricultural Cities -->
                                <optgroup label="🇮🇳 India - Major Agricultural Centers">
                                    <option value="Delhi" {{ 'selected' if city == 'Delhi' else '' }}>Delhi - National Capital</option>
                                    <option value="Mumbai" {{ 'selected' if city == 'Mumbai' else '' }}>Mumbai - Maharashtra</option>
                                    <option value="Bangalore" {{ 'selected' if city == 'Bangalore' else '' }}>Bangalore - Karnataka</option>
                                    <option value="Chennai" {{ 'selected' if city == 'Chennai' else '' }}>Chennai - Tamil Nadu</option>
                                    <option value="Kolkata" {{ 'selected' if city == 'Kolkata' else '' }}>Kolkata - West Bengal</option>
                                    <option value="Hyderabad" {{ 'selected' if city == 'Hyderabad' else '' }}>Hyderabad - Telangana</option>
                                    <option value="Pune" {{ 'selected' if city == 'Pune' else '' }}>Pune - Maharashtra</option>
                                    <option value="Ahmedabad" {{ 'selected' if city == 'Ahmedabad' else '' }}>Ahmedabad - Gujarat</option>
                                    <option value="Jaipur" {{ 'selected' if city == 'Jaipur' else '' }}>Jaipur - Rajasthan</option>
                                    <option value="Lucknow" {{ 'selected' if city == 'Lucknow' else '' }}>Lucknow - Uttar Pradesh</option>
                                </optgroup>

                                <!-- Punjab - Wheat Belt -->
                                <optgroup label="🌾 Punjab - Wheat & Rice Belt">
                                    <option value="Chandigarh" {{ 'selected' if city == 'Chandigarh' else '' }}>Chandigarh</option>
                                    <option value="Ludhiana" {{ 'selected' if city == 'Ludhiana' else '' }}>Ludhiana</option>
                                    <option value="Amritsar" {{ 'selected' if city == 'Amritsar' else '' }}>Amritsar</option>
                                    <option value="Jalandhar" {{ 'selected' if city == 'Jalandhar' else '' }}>Jalandhar</option>
                                    <option value="Patiala" {{ 'selected' if city == 'Patiala' else '' }}>Patiala</option>
                                </optgroup>

                                <!-- Haryana - Green Revolution -->
                                <optgroup label="🌱 Haryana - Green Revolution Hub">
                                    <option value="Gurgaon" {{ 'selected' if city == 'Gurgaon' else '' }}>Gurgaon</option>
                                    <option value="Faridabad" {{ 'selected' if city == 'Faridabad' else '' }}>Faridabad</option>
                                    <option value="Panipat" {{ 'selected' if city == 'Panipat' else '' }}>Panipat</option>
                                    <option value="Karnal" {{ 'selected' if city == 'Karnal' else '' }}>Karnal</option>
                                    <option value="Hisar" {{ 'selected' if city == 'Hisar' else '' }}>Hisar</option>
                                </optgroup>

                                <!-- Uttar Pradesh - Sugar Belt -->
                                <optgroup label="🍯 Uttar Pradesh - Sugar & Wheat Belt">
                                    <option value="Kanpur" {{ 'selected' if city == 'Kanpur' else '' }}>Kanpur</option>
                                    <option value="Agra" {{ 'selected' if city == 'Agra' else '' }}>Agra</option>
                                    <option value="Varanasi" {{ 'selected' if city == 'Varanasi' else '' }}>Varanasi</option>
                                    <option value="Meerut" {{ 'selected' if city == 'Meerut' else '' }}>Meerut</option>
                                    <option value="Allahabad" {{ 'selected' if city == 'Allahabad' else '' }}>Allahabad</option>
                                </optgroup>

                                <!-- Maharashtra - Cotton & Sugarcane -->
                                <optgroup label="🌿 Maharashtra - Cotton & Sugarcane">
                                    <option value="Nagpur" {{ 'selected' if city == 'Nagpur' else '' }}>Nagpur</option>
                                    <option value="Nashik" {{ 'selected' if city == 'Nashik' else '' }}>Nashik</option>
                                    <option value="Aurangabad" {{ 'selected' if city == 'Aurangabad' else '' }}>Aurangabad</option>
                                    <option value="Solapur" {{ 'selected' if city == 'Solapur' else '' }}>Solapur</option>
                                    <option value="Kolhapur" {{ 'selected' if city == 'Kolhapur' else '' }}>Kolhapur</option>
                                </optgroup>

                                <!-- Gujarat - Cotton & Groundnut -->
                                <optgroup label="🥜 Gujarat - Cotton & Groundnut Hub">
                                    <option value="Surat" {{ 'selected' if city == 'Surat' else '' }}>Surat</option>
                                    <option value="Vadodara" {{ 'selected' if city == 'Vadodara' else '' }}>Vadodara</option>
                                    <option value="Rajkot" {{ 'selected' if city == 'Rajkot' else '' }}>Rajkot</option>
                                    <option value="Bhavnagar" {{ 'selected' if city == 'Bhavnagar' else '' }}>Bhavnagar</option>
                                    <option value="Junagadh" {{ 'selected' if city == 'Junagadh' else '' }}>Junagadh</option>
                                </optgroup>

                                <!-- Tamil Nadu - Rice & Sugarcane -->
                                <optgroup label="🌾 Tamil Nadu - Rice & Sugarcane">
                                    <option value="Coimbatore" {{ 'selected' if city == 'Coimbatore' else '' }}>Coimbatore</option>
                                    <option value="Madurai" {{ 'selected' if city == 'Madurai' else '' }}>Madurai</option>
                                    <option value="Salem" {{ 'selected' if city == 'Salem' else '' }}>Salem</option>
                                    <option value="Tiruchirappalli" {{ 'selected' if city == 'Tiruchirappalli' else '' }}>Tiruchirappalli</option>
                                    <option value="Thanjavur" {{ 'selected' if city == 'Thanjavur' else '' }}>Thanjavur</option>
                                </optgroup>

                                <!-- Karnataka - Coffee & Spices -->
                                <optgroup label="☕ Karnataka - Coffee & Spices">
                                    <option value="Mysore" {{ 'selected' if city == 'Mysore' else '' }}>Mysore</option>
                                    <option value="Hubli" {{ 'selected' if city == 'Hubli' else '' }}>Hubli</option>
                                    <option value="Mangalore" {{ 'selected' if city == 'Mangalore' else '' }}>Mangalore</option>
                                    <option value="Belgaum" {{ 'selected' if city == 'Belgaum' else '' }}>Belgaum</option>
                                    <option value="Shimoga" {{ 'selected' if city == 'Shimoga' else '' }}>Shimoga</option>
                                </optgroup>

                                <!-- Andhra Pradesh & Telangana - Rice & Cotton -->
                                <optgroup label="🌾 Andhra Pradesh & Telangana - Rice & Cotton">
                                    <option value="Visakhapatnam" {{ 'selected' if city == 'Visakhapatnam' else '' }}>Visakhapatnam</option>
                                    <option value="Vijayawada" {{ 'selected' if city == 'Vijayawada' else '' }}>Vijayawada</option>
                                    <option value="Guntur" {{ 'selected' if city == 'Guntur' else '' }}>Guntur</option>
                                    <option value="Warangal" {{ 'selected' if city == 'Warangal' else '' }}>Warangal</option>
                                    <option value="Nizamabad" {{ 'selected' if city == 'Nizamabad' else '' }}>Nizamabad</option>
                                </optgroup>

                                <!-- International Agricultural Cities -->
                                <optgroup label="🌍 International Agricultural Centers">
                                    <option value="Des Moines" {{ 'selected' if city == 'Des Moines' else '' }}>Des Moines, USA - Corn Belt</option>
                                    <option value="Fresno" {{ 'selected' if city == 'Fresno' else '' }}>Fresno, USA - Central Valley</option>
                                    <option value="Winnipeg" {{ 'selected' if city == 'Winnipeg' else '' }}>Winnipeg, Canada - Wheat</option>
                                    <option value="Buenos Aires" {{ 'selected' if city == 'Buenos Aires' else '' }}>Buenos Aires, Argentina</option>
                                    <option value="São Paulo" {{ 'selected' if city == 'São Paulo' else '' }}>São Paulo, Brazil</option>
                                    <option value="Melbourne" {{ 'selected' if city == 'Melbourne' else '' }}>Melbourne, Australia</option>
                                    <option value="Kiev" {{ 'selected' if city == 'Kiev' else '' }}>Kiev, Ukraine - Grain Hub</option>
                                    <option value="Bangkok" {{ 'selected' if city == 'Bangkok' else '' }}>Bangkok, Thailand - Rice</option>
                                </optgroup>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Select from major agricultural cities worldwide
                            </div>
                        </div>

                        <!-- Manual City Input -->
                        <div class="mb-3" id="city_manual_section" style="display: none;">
                            <label for="city_manual" class="form-label fw-semibold">
                                <i class="fas fa-edit text-warning me-2"></i>Enter City Name
                            </label>
                            <input
                                type="text"
                                class="form-control form-control-lg"
                                id="city_manual"
                                placeholder="Enter any city name (e.g., Delhi, Mumbai, London)"
                                name="city_manual"
                                value="{{ city if city else '' }}"
                            />
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Type any city name for weather data
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-cloud-download-alt me-2"></i>Get Weather Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access to Popular Agricultural Cities -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Quick Access - Popular Agricultural Cities
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="row g-2">
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Delhi')">
                                <i class="fas fa-city me-1"></i>Delhi
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Mumbai')">
                                <i class="fas fa-building me-1"></i>Mumbai
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Bangalore')">
                                <i class="fas fa-leaf me-1"></i>Bangalore
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Chennai')">
                                <i class="fas fa-water me-1"></i>Chennai
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Ludhiana')">
                                <i class="fas fa-seedling me-1"></i>Ludhiana
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectPopularCity('Nagpur')">
                                <i class="fas fa-sun me-1"></i>Nagpur
                            </button>
                        </div>
                    </div>
                    <div class="row g-2 mt-1">
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Pune')">
                                <i class="fas fa-mountain me-1"></i>Pune
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Jaipur')">
                                <i class="fas fa-gem me-1"></i>Jaipur
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Ahmedabad')">
                                <i class="fas fa-industry me-1"></i>Ahmedabad
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Hyderabad')">
                                <i class="fas fa-mosque me-1"></i>Hyderabad
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Kolkata')">
                                <i class="fas fa-bridge me-1"></i>Kolkata
                            </button>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                            <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="selectPopularCity('Chandigarh')">
                                <i class="fas fa-tree me-1"></i>Chandigarh
                            </button>
                        </div>
                    </div>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Click any city for instant weather data
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if city %}
    <!-- Current Weather Display -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient text-white text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h3 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>{{ city }}
                        <small class="ms-3 opacity-75">{{ time if time else 'Now' }}</small>
                    </h3>
                </div>
                <div class="card-body p-0">
                    <div class="row g-0">
                        <!-- Main Weather Info -->
                        <div class="col-lg-4 bg-primary text-white p-4 d-flex align-items-center">
                            <div class="text-center w-100">
                                <div class="weather-icon mb-3">
                                    {% if 'clear' in sky.lower() %}
                                        <i class="fas fa-sun fa-4x text-warning"></i>
                                    {% elif 'cloud' in sky.lower() %}
                                        <i class="fas fa-cloud fa-4x text-light"></i>
                                    {% elif 'rain' in sky.lower() %}
                                        <i class="fas fa-cloud-rain fa-4x text-info"></i>
                                    {% else %}
                                        <i class="fas fa-cloud-sun fa-4x text-warning"></i>
                                    {% endif %}
                                </div>
                                <h1 class="display-3 fw-bold mb-0">{{ temp if temp else 'N/A' }}</h1>
                                <h4 class="mb-0 opacity-75">{{ sky if sky else 'Clear Sky' }}</h4>
                            </div>
                        </div>

                        <!-- Weather Details -->
                        <div class="col-lg-8 p-4">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="weather-detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-thermometer-half fa-2x text-danger me-3"></i>
                                            <div>
                                                <h6 class="mb-0 text-muted">Temperature</h6>
                                                <h4 class="mb-0 fw-bold">{{ temp if temp else 'N/A' }}</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="weather-detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tint fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="mb-0 text-muted">Humidity</h6>
                                                <h4 class="mb-0 fw-bold">{{ humidity if humidity else 'N/A' }}%</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="weather-detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-wind fa-2x text-success me-3"></i>
                                            <div>
                                                <h6 class="mb-0 text-muted">Wind Speed</h6>
                                                <h4 class="mb-0 fw-bold">{{ wind_speed if wind_speed else 'N/A' }} km/h</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="weather-detail-card p-3 bg-light rounded">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-eye fa-2x text-info me-3"></i>
                                            <div>
                                                <h6 class="mb-0 text-muted">Visibility</h6>
                                                <h4 class="mb-0 fw-bold">{{ visibility if visibility else 'N/A' }} km</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Agricultural Weather Insights -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-seedling me-2"></i>Agricultural Weather Insights
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-lg-4">
                            <div class="insight-card p-3 border rounded bg-light">
                                <h6 class="text-success fw-bold">
                                    <i class="fas fa-tractor me-2"></i>Farming Conditions
                                </h6>
                                <p class="mb-0">
                                    {% if temp and temp|replace('°C', '')|float > 30 %}
                                        <span class="badge bg-warning">Hot</span> Consider irrigation and shade for crops
                                    {% elif temp and temp|replace('°C', '')|float < 15 %}
                                        <span class="badge bg-info">Cool</span> Monitor for frost protection
                                    {% else %}
                                        <span class="badge bg-success">Optimal</span> Good conditions for farming activities
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="insight-card p-3 border rounded bg-light">
                                <h6 class="text-primary fw-bold">
                                    <i class="fas fa-cloud-rain me-2"></i>Irrigation Advice
                                </h6>
                                <p class="mb-0">
                                    {% if 'rain' in sky.lower() %}
                                        <span class="badge bg-primary">Rain Expected</span> Reduce irrigation schedule
                                    {% elif humidity and humidity|float > 70 %}
                                        <span class="badge bg-info">High Humidity</span> Monitor for fungal diseases
                                    {% else %}
                                        <span class="badge bg-warning">Dry Conditions</span> Increase irrigation frequency
                                    {% endif %}
                                </p>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="insight-card p-3 border rounded bg-light">
                                <h6 class="text-danger fw-bold">
                                    <i class="fas fa-exclamation-triangle me-2"></i>Weather Alerts
                                </h6>
                                <p class="mb-0">
                                    {% if wind_speed and wind_speed|replace(' km/h', '')|float > 25 %}
                                        <span class="badge bg-danger">High Wind</span> Secure equipment and structures
                                    {% elif 'storm' in sky.lower() or 'thunder' in sky.lower() %}
                                        <span class="badge bg-danger">Storm Warning</span> Avoid field operations
                                    {% else %}
                                        <span class="badge bg-success">No Alerts</span> Safe for farming activities
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if msg %}
    <!-- Error Message -->
    <div class="row">
        <div class="col-lg-6 mx-auto">
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Weather Data Unavailable:</strong> {{ msg }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Weather Tips for Farmers -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Weather-Based Farming Tips
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="tip-card p-3 border-start border-success border-4 bg-light">
                                <h6 class="text-success fw-bold">☀️ Sunny Weather</h6>
                                <ul class="mb-0 small">
                                    <li>Perfect for harvesting and drying crops</li>
                                    <li>Increase irrigation frequency</li>
                                    <li>Apply pesticides in early morning</li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="tip-card p-3 border-start border-primary border-4 bg-light">
                                <h6 class="text-primary fw-bold">🌧️ Rainy Weather</h6>
                                <ul class="mb-0 small">
                                    <li>Avoid field operations and spraying</li>
                                    <li>Monitor for waterlogging</li>
                                    <li>Check for fungal diseases</li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="tip-card p-3 border-start border-warning border-4 bg-light">
                                <h6 class="text-warning fw-bold">💨 Windy Weather</h6>
                                <ul class="mb-0 small">
                                    <li>Avoid spraying operations</li>
                                    <li>Secure greenhouse structures</li>
                                    <li>Monitor for crop lodging</li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="tip-card p-3 border-start border-info border-4 bg-light">
                                <h6 class="text-info fw-bold">❄️ Cold Weather</h6>
                                <ul class="mb-0 small">
                                    <li>Protect crops from frost</li>
                                    <li>Delay planting of warm-season crops</li>
                                    <li>Use row covers for protection</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.weather-detail-card {
    transition: all 0.3s ease;
}

.weather-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.insight-card, .tip-card {
    transition: all 0.3s ease;
}

.insight-card:hover, .tip-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.weather-icon {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}
</style>

<script>
// City selection method toggle
document.addEventListener('DOMContentLoaded', function() {
    const dropdownMethod = document.getElementById('dropdown_method');
    const manualMethod = document.getElementById('manual_method');
    const dropdownSection = document.getElementById('city_dropdown_section');
    const manualSection = document.getElementById('city_manual_section');
    const citySelect = document.getElementById('city_select');
    const cityManual = document.getElementById('city_manual');

    // Toggle between dropdown and manual input
    function toggleCityInput() {
        if (dropdownMethod.checked) {
            dropdownSection.style.display = 'block';
            manualSection.style.display = 'none';
            citySelect.required = true;
            cityManual.required = false;
        } else {
            dropdownSection.style.display = 'none';
            manualSection.style.display = 'block';
            citySelect.required = false;
            cityManual.required = true;
        }
    }

    // Event listeners for radio buttons
    dropdownMethod.addEventListener('change', toggleCityInput);
    manualMethod.addEventListener('change', toggleCityInput);

    // Initialize the correct view
    toggleCityInput();

    // Form submission handler
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let cityValue = '';

        if (dropdownMethod.checked) {
            cityValue = citySelect.value;
            if (!cityValue) {
                e.preventDefault();
                alert('Please select a city from the dropdown.');
                return;
            }
            // Create hidden input for city
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'city';
            hiddenInput.value = cityValue;
            form.appendChild(hiddenInput);
        } else {
            cityValue = cityManual.value.trim();
            if (!cityValue) {
                e.preventDefault();
                alert('Please enter a city name.');
                return;
            }
            // Create hidden input for city
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'city';
            hiddenInput.value = cityValue;
            form.appendChild(hiddenInput);
        }
    });

    // Quick city search functionality
    citySelect.addEventListener('change', function() {
        if (this.value) {
            // Add visual feedback
            this.style.borderColor = '#28a745';
            this.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
        }
    });

    // Auto-complete for manual input
    cityManual.addEventListener('input', function() {
        const value = this.value.toLowerCase();
        if (value.length > 2) {
            // Add visual feedback
            this.style.borderColor = '#007bff';
            this.style.boxShadow = '0 0 0 0.2rem rgba(0, 123, 255, 0.25)';
        }
    });
});

// Auto-refresh weather data every 10 minutes
setInterval(function() {
    const currentCity = document.querySelector('input[name="city"]');
    if (currentCity && currentCity.value) {
        console.log('Auto-refreshing weather data for:', currentCity.value);
        // Could implement auto-refresh here
    }
}, 600000); // 10 minutes

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Popular cities quick access
function selectPopularCity(cityName) {
    const citySelect = document.getElementById('city_select');
    const dropdownMethod = document.getElementById('dropdown_method');

    // Switch to dropdown method
    dropdownMethod.checked = true;
    dropdownMethod.dispatchEvent(new Event('change'));

    // Select the city
    citySelect.value = cityName;
    citySelect.dispatchEvent(new Event('change'));

    // Submit the form
    document.querySelector('form').submit();
}
</script>
{% endblock %}