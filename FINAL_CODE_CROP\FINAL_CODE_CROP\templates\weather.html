{% extends 'layout.html' %} {% block body %}
<link href="http://127.0.0.1:5000/static/css/stywea.css" rel="stylesheet" />

<style>
  html body {
    /* background-color: rgb(206, 206, 228); */
    background-color: rgb(104 108 115);
  }
  body{
    background-image: url("https://dynamicpowerpoint.com/wp-content/uploads/2022/02/rain-and-clouds-full-hd-weather-icon-sample.gif");
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: 100% 95vh;
    height: 1200px;
  } 
  .card {
        background-image: url("{{url_for('static',filename='img.jpeg')}}");
        background-size: cover;
        border-radius: 20px;
        box-shadow: 0px 8px 16px 4px #9e9e9e;
        color: white;
        padding: 40px;
        height: 300px;
  }
  h3{
    color: aliceblue;
    font-weight: bold;
  }
</style>
<!--Form Section-->
<br /><br />

  <div class="container">
       
    <div class="row">
              <div class="col-lg-6">
                <form method="POST" action="/weather">
                  <h3>Weather Information</h3>
                  <div class="form-group">
                    <label for="city">City:</label>
                    <input
                      type="text"
                      class="form-control"
                      id="city"
                      placeholder="Enter city name"
                      name="city"
                      required
                    />
                  </div>
                  <br />
                  <button type="submit" class="btn btn-success" style="width: 25%">
                    Submit
                  </button>
                </form>
              </div>
      
              
              <div class="col-lg-6">
                <div class="card">
                  {% if city %}
                  <h3 class="ml-auto mr-4 mt-3 mb-0">{{city}}</h3>
                  <h3 class="ml-auto mr-4 mb-0 med-font">{{temp}}</h3>
                  <h3 class="ml-auto mr-4 large-font">{{sky}}</h3>
                  <h3 class="time-font mb-0 ml-4 mt-auto">{{time}}</h3>
                  {% endif %}

                  {% if msg %}
                  <p>{{msg}}</p>
                  {% endif %}
                </div>
              </div>
              
            </div>
          </div>
      
          <!-- Bootstrap core JS-->
          <script src="https://cdn.startbootstrap.com/sb-forms-latest.js"></script>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.0.3/css/font-awesome.css"></script>
          <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
          <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
          <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css"></script>
          <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js"></script>
          <!-- Core theme JS-->
          <script src="http://127.0.0.1:5000/static/js/scripts.js"></script>

          {% endblock %}