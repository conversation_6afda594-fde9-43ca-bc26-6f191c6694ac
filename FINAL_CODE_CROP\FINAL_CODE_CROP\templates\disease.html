{% extends 'layout.html' %}

{% block content %}
<div class="container py-5" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); min-height: 100vh;">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card card-modern shadow-lg">
        <div class="card-header card-header-modern text-center">
          <h2 class="mb-0">
            <i class="fas fa-bug me-2"></i>
            Plant Disease Detection
          </h2>
          <p class="mb-0 mt-2 opacity-75">Upload an image to identify plant diseases using AI</p>
        </div>
        <div class="card-body p-5">
          <form class="needs-validation" method="post" enctype="multipart/form-data" novalidate>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-4">
                  <label for="inputfile" class="form-label fw-semibold">
                    <i class="fas fa-camera me-2 text-primary"></i>Upload Plant Image
                  </label>
                  <input
                    type="file"
                    name="file"
                    class="form-control form-control-modern"
                    id="inputfile"
                    onchange="preview_image(event)"
                    accept="image/*"
                    required
                  >
                  <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    Supported formats: JPG, PNG, JPEG (Max 5MB)
                  </div>
                </div>

                <div class="text-center">
                  <button class="btn btn-primary-modern btn-lg px-5 py-3" type="submit">
                    <i class="fas fa-search me-2"></i>Analyze Disease
                  </button>
                </div>
              </div>

              <div class="col-md-6">
                <div class="image-preview-container">
                  <label class="form-label fw-semibold">
                    <i class="fas fa-eye me-2 text-success"></i>Image Preview
                  </label>
                  <div class="preview-box border rounded-4 p-3 text-center" style="min-height: 300px; background: #f8f9fa;">
                    <img
                      id="output-image"
                      class="img-fluid rounded-3 shadow-sm"
                      style="max-height: 280px; display: none;"
                    />
                    <div id="preview-placeholder" class="d-flex flex-column align-items-center justify-content-center h-100">
                      <i class="fas fa-image fa-3x text-muted mb-3"></i>
                      <p class="text-muted">Image preview will appear here</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>

      <!-- Information Cards -->
      <div class="row mt-4">
        <div class="col-md-4">
          <div class="card border-0 bg-light h-100">
            <div class="card-body text-center">
              <i class="fas fa-microscope fa-2x text-primary mb-3"></i>
              <h6 class="fw-bold">AI-Powered Analysis</h6>
              <p class="small text-muted">Advanced machine learning algorithms for accurate disease detection</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 bg-light h-100">
            <div class="card-body text-center">
              <i class="fas fa-clock fa-2x text-success mb-3"></i>
              <h6 class="fw-bold">Instant Results</h6>
              <p class="small text-muted">Get disease identification and treatment recommendations in seconds</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-0 bg-light h-100">
            <div class="card-body text-center">
              <i class="fas fa-leaf fa-2x text-warning mb-3"></i>
              <h6 class="fw-bold">Multiple Crops</h6>
              <p class="small text-muted">Supports detection for various crops including tomato, potato, corn, and apple</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  function preview_image(event) {
    var reader = new FileReader();
    reader.onload = function () {
      var output = document.getElementById('output-image');
      var placeholder = document.getElementById('preview-placeholder');

      output.src = reader.result;
      output.style.display = 'block';
      placeholder.style.display = 'none';
    }
    reader.readAsDataURL(event.target.files[0]);
  }
</script>
{% endblock %}